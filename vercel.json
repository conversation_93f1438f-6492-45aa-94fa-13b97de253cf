{"version": 2, "name": "faafo", "installCommand": "cd faafo-career-platform && npm install --legacy-peer-deps", "buildCommand": "cd faafo-career-platform && npm run build", "outputDirectory": "faafo-career-platform/.next", "framework": "nextjs", "regions": ["iad1"], "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "redirects": [{"source": "/docs", "destination": "/api-docs", "permanent": true}]}