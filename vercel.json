{"version": 2, "name": "faafo", "installCommand": "npm install --legacy-peer-deps", "buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "regions": ["iad1"], "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "redirects": [{"source": "/docs", "destination": "/api-docs", "permanent": true}]}