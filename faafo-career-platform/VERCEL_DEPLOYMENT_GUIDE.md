# 🚀 Vercel Deployment Guide - FAAFO Career Platform

## Overview
This guide provides step-by-step instructions for deploying the FAAFO Career Platform to Vercel with production-ready configuration.

## Prerequisites

### ✅ Pre-deployment Checklist
- [ ] Production build passes (`npm run build`)
- [ ] Database is configured and accessible
- [ ] Environment variables are properly set
- [ ] Vercel CLI is installed
- [ ] GitHub repository is ready (optional but recommended)

## 🔧 Environment Variables Setup

### Required Environment Variables
Configure these in your Vercel project settings:

```bash
# Core Configuration
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.vercel.app
NEXTAUTH_SECRET=your-production-secret-here

# Database (Already configured)
DATABASE_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require

# Email Service
RESEND_API_KEY=re_d4e8Qnct_JBiErHhAon7wq63CGsYqnotx
EMAIL_FROM=<EMAIL>
```

### Optional Environment Variables
```bash
# AI Features (if using)
GOOGLE_GEMINI_API_KEY=your-api-key
GEMINI_MODEL=gemini-pro

# Error Monitoring
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS=your-ga-id
```

## 🚀 Deployment Methods

### Method 1: Automated Script (Recommended)
```bash
# Run the deployment script
./scripts/deploy-to-vercel.sh
```

### Method 2: Manual Vercel CLI
```bash
# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

### Method 3: GitHub Integration (Recommended for CI/CD)
1. Push your code to GitHub
2. Connect repository to Vercel
3. Configure environment variables in Vercel dashboard
4. Enable automatic deployments

## 📋 Step-by-Step Deployment

### Step 1: Install Vercel CLI
```bash
npm install -g vercel
```

### Step 2: Login to Vercel
```bash
vercel login
```

### Step 3: Configure Environment Variables
Either use the Vercel dashboard or CLI:

```bash
# Set environment variables via CLI
vercel env add NEXTAUTH_SECRET
vercel env add DATABASE_URL
vercel env add RESEND_API_KEY
```

### Step 4: Deploy
```bash
# Preview deployment
vercel

# Production deployment
vercel --prod
```

## 🔧 Vercel Configuration

The project includes optimized `vercel.json` configuration:

- **Framework**: Next.js 15
- **Node.js Version**: 18.x
- **Build Command**: `npm run build`
- **Output Directory**: `.next`
- **Function Timeout**: 30 seconds
- **Security Headers**: Configured
- **Redirects**: Set up for API docs

## 🗄️ Database Setup

### Database Migration
```bash
# Apply migrations to production database
npx prisma migrate deploy

# Generate Prisma client
npx prisma generate
```

### Database Seeding (Optional)
```bash
# Seed production database with initial data
npm run prisma:seed
```

## 🧪 Testing Production Deployment

### 1. Functionality Tests
- [ ] User registration and login
- [ ] Email verification
- [ ] Assessment functionality
- [ ] Profile management
- [ ] Forum features
- [ ] Freedom Fund calculator

### 2. Performance Tests
- [ ] Page load times
- [ ] API response times
- [ ] Database query performance
- [ ] Image optimization

### 3. Security Tests
- [ ] HTTPS enforcement
- [ ] Security headers
- [ ] Authentication flows
- [ ] Data validation

## 🔍 Monitoring & Debugging

### Vercel Function Logs
```bash
# View function logs
vercel logs

# Real-time logs
vercel logs --follow
```

### Performance Monitoring
- Use Vercel Analytics
- Monitor Core Web Vitals
- Track API response times
- Database connection monitoring

## 🌐 Custom Domain Setup

### 1. Add Domain in Vercel Dashboard
1. Go to Project Settings → Domains
2. Add your custom domain
3. Configure DNS records

### 2. Update Environment Variables
```bash
NEXTAUTH_URL=https://your-custom-domain.com
```

## 🔒 Security Considerations

### Production Security Checklist
- [ ] Strong NEXTAUTH_SECRET (32+ characters)
- [ ] HTTPS enforcement
- [ ] Security headers configured
- [ ] Database connection secured
- [ ] API rate limiting enabled
- [ ] Input validation implemented

## 🚨 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check build logs
vercel logs --since=1h

# Test build locally
npm run build
```

#### Database Connection Issues
```bash
# Test database connection
npm run test:db-connection

# Check environment variables
vercel env ls
```

#### Function Timeouts
- Increase function timeout in `vercel.json`
- Optimize database queries
- Implement caching

## 📊 Performance Optimization

### 1. Database Optimization
- Connection pooling configured
- Query optimization
- Proper indexing

### 2. Caching Strategy
- Static page caching
- API response caching
- Database query caching

### 3. Asset Optimization
- Image optimization enabled
- CSS/JS minification
- Gzip compression

## 🔄 CI/CD Pipeline

### GitHub Actions (Optional)
Create `.github/workflows/deploy.yml` for automated testing and deployment:

```yaml
name: Deploy to Vercel
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm install
      - name: Run tests
        run: npm test
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

## 📞 Support

### Getting Help
- Check Vercel documentation
- Review function logs
- Test locally first
- Check environment variables
- Verify database connectivity

### Useful Commands
```bash
# Check deployment status
vercel ls

# View project info
vercel inspect

# Remove deployment
vercel rm [deployment-url]
```

---

## 🎉 Deployment Complete!

Once deployed successfully:
1. Test all functionality
2. Monitor performance
3. Set up alerts
4. Configure custom domain
5. Enable analytics
6. Document any custom configurations

Your FAAFO Career Platform is now live on Vercel! 🚀
