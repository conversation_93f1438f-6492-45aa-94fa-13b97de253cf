import React from 'react';

// Conditional Sentry import
let Sentry: any = null;
try {
  Sentry = require('@sentry/nextjs');
} catch (error) {
  // Sentry not available, will use console logging instead
}

export interface ErrorContext {
  userId?: string;
  userEmail?: string;
  action?: string;
  component?: string;
  additionalData?: Record<string, any>;
}

export class ErrorReporter {
  /**
   * Report an error to <PERSON>try with additional context
   */
  static captureError(error: Error, context?: ErrorContext) {
    if (Sentry) {
      Sentry.withScope((scope: any) => {
        if (context?.userId) {
          scope.setUser({ id: context.userId, email: context.userEmail });
        }

        if (context?.action) {
          scope.setTag('action', context.action);
        }

        if (context?.component) {
          scope.setTag('component', context.component);
        }

        if (context?.additionalData) {
          scope.setContext('additionalData', context.additionalData);
        }

        Sentry.captureException(error);
      });
    } else {
      // Fallback to console logging when Sentry is not available
      console.error('Error captured:', error);
      if (context) {
        console.error('Error context:', context);
      }
    }
  }

  /**
   * Report a message to <PERSON><PERSON> (for non-error events)
   */
  static captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info', context?: ErrorContext) {
    if (Sentry) {
      Sentry.withScope((scope: any) => {
        if (context?.userId) {
          scope.setUser({ id: context.userId, email: context.userEmail });
        }

        if (context?.action) {
          scope.setTag('action', context.action);
        }

        if (context?.component) {
          scope.setTag('component', context.component);
        }

        if (context?.additionalData) {
          scope.setContext('additionalData', context.additionalData);
        }

        Sentry.captureMessage(message, level);
      });
    } else {
      // Fallback to console logging
      console.log(`[${level.toUpperCase()}] ${message}`);
      if (context) {
        console.log('Message context:', context);
      }
    }
  }

  /**
   * Add breadcrumb for debugging
   */
  static addBreadcrumb(message: string, category?: string, data?: Record<string, any>) {
    if (Sentry) {
      Sentry.addBreadcrumb({
        message,
        category: category || 'custom',
        data,
        level: 'info',
      });
    } else {
      // Fallback to console logging
      console.log(`[BREADCRUMB] ${category || 'custom'}: ${message}`, data);
    }
  }

  /**
   * Set user context for all subsequent error reports
   */
  static setUser(userId: string, email?: string, additionalData?: Record<string, any>) {
    if (Sentry) {
      Sentry.setUser({
        id: userId,
        email,
        ...additionalData,
      });
    } else {
      console.log(`[USER] Set user context: ${userId} (${email})`);
    }
  }

  /**
   * Clear user context
   */
  static clearUser() {
    if (Sentry) {
      Sentry.setUser(null);
    } else {
      console.log('[USER] Cleared user context');
    }
  }

  /**
   * Performance monitoring - start transaction
   */
  static startTransaction(name: string, operation: string) {
    if (Sentry) {
      // Note: startTransaction is deprecated in newer Sentry versions
      // Using startSpan instead for performance monitoring
      return Sentry.startSpan({ name, op: operation }, () => {});
    } else {
      console.log(`[PERFORMANCE] Start transaction: ${name} (${operation})`);
      return null;
    }
  }

  /**
   * Performance monitoring - measure function execution
   */
  static async measureAsync<T>(
    name: string,
    operation: string,
    fn: () => Promise<T>
  ): Promise<T> {
    if (Sentry) {
      return Sentry.startSpan({ name, op: operation }, async () => {
        try {
          const result = await fn();
          return result;
        } catch (error) {
          Sentry.captureException(error);
          throw error;
        }
      });
    } else {
      console.log(`[PERFORMANCE] Measuring: ${name} (${operation})`);
      try {
        const result = await fn();
        console.log(`[PERFORMANCE] Completed: ${name}`);
        return result;
      } catch (error) {
        console.error(`[PERFORMANCE] Error in ${name}:`, error);
        throw error;
      }
    }
  }
}

/**
 * Higher-order component for automatic error boundary with Sentry reporting
 */
export function withErrorReporting<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
): React.ComponentType<P> {
  const WithErrorReportingComponent: React.ComponentType<P> = (props: P) => {
    React.useEffect(() => {
      ErrorReporter.addBreadcrumb(
        `Component ${componentName || WrappedComponent.name} mounted`,
        'component',
        { componentName: componentName || WrappedComponent.name }
      );
    }, []);

    return React.createElement(WrappedComponent, props);
  };

  WithErrorReportingComponent.displayName = `withErrorReporting(${componentName || WrappedComponent.name})`;

  return WithErrorReportingComponent;
}

/**
 * Hook for error reporting in functional components
 */
export function useErrorReporting() {
  const reportError = React.useCallback((error: Error, context?: ErrorContext) => {
    ErrorReporter.captureError(error, context);
  }, []);

  const reportMessage = React.useCallback((
    message: string, 
    level: 'info' | 'warning' | 'error' = 'info', 
    context?: ErrorContext
  ) => {
    ErrorReporter.captureMessage(message, level, context);
  }, []);

  const addBreadcrumb = React.useCallback((
    message: string, 
    category?: string, 
    data?: Record<string, any>
  ) => {
    ErrorReporter.addBreadcrumb(message, category, data);
  }, []);

  return {
    reportError,
    reportMessage,
    addBreadcrumb,
  };
}
