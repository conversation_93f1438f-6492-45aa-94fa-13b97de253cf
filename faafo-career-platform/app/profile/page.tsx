'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import EnhancedProfileForm from '@/components/profile/EnhancedProfileForm';

interface ProfileData {
  id?: string;
  bio?: string;
  profilePictureUrl?: string;
  socialMediaLinks?: { [key: string]: string };

  // Phase 2 Enhanced Profile Fields
  firstName?: string;
  lastName?: string;
  jobTitle?: string;
  company?: string;
  location?: string;
  phoneNumber?: string;
  website?: string;

  // Career-related fields
  careerInterests?: string[];
  skillsToLearn?: string[];
  experienceLevel?: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  currentIndustry?: string;
  targetIndustry?: string;

  // Profile completion tracking
  profileCompletionScore?: number;
  lastProfileUpdate?: string;

  // Preferences
  weeklyLearningGoal?: number;
  emailNotifications?: boolean;
  profileVisibility?: 'PRIVATE' | 'PUBLIC' | 'COMMUNITY_ONLY';
  profilePublic?: boolean;
  showEmail?: boolean;
  showPhone?: boolean;
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (status === 'authenticated') {
      fetchProfile();
    } else if (status === 'unauthenticated') {
      // Redirect or show message if not logged in
      // For now, just stop loading and show nothing or a message
      setIsLoading(false);
      // router.push('/login'); // Example redirect
    }
  }, [status]);

  const fetchProfile = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/profile');
      if (!res.ok) {
        throw new Error(`Failed to fetch profile: ${res.statusText} (status: ${res.status})`);
      }
      const data: ProfileData = await res.json();
      setProfile(data);
    } catch (err: unknown) {
      console.error(err);
      if (err instanceof Error) {
        setError(err.message || 'Failed to load profile.');
      } else {
        setError('An unknown error occurred while loading profile.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async (formData: ProfileData): Promise<{ success: boolean; message?: string }> => {
    setIsLoading(true);
    setError(null);
    try {
      const res = await fetch('/api/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });
      if (!res.ok) {
        // Try to parse error message from response, otherwise use statusText
        let errorMessage = `Failed to update profile: ${res.statusText} (status: ${res.status})`;
        try {
          const errorData = await res.json();
          errorMessage = errorData.message || errorMessage;
        } catch {
          // Ignore if response is not JSON or parsing fails, variable 'e' is not needed
        }
        throw new Error(errorMessage);
      }
      const updatedProfile: ProfileData = await res.json();
      setProfile(updatedProfile);
      return { success: true, message: 'Profile updated successfully!' };
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred while saving profile.';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  if (status === 'loading' || isLoading) {
    return <p>Loading...</p>;
  }

  if (status === 'unauthenticated') {
    return <p>Please log in to view your profile.</p>; // Or redirect
  }

  if (error) {
    return (
      <div>
        <p role="alert">Error: {error}</p>
        <button onClick={fetchProfile} className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          Try Again
        </button>
      </div>
    );
  }

  if (!profile) {
    return <p>No profile data found.</p>;
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Your Profile</h1>
        {session?.user?.email && <p className="text-gray-600 dark:text-gray-400 mb-4">Email: {session.user.email}</p>}

        {/* Profile Completion Progress */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-4 mb-6 shadow-sm border">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-lg font-semibold">Profile Completion</h2>
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
              {profile.profileCompletionScore || 0}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${profile.profileCompletionScore || 0}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            Complete your profile to get better career recommendations and connect with the community.
          </p>
        </div>
      </div>

      <EnhancedProfileForm initialData={profile} onSave={handleSave} />
    </div>
  );
}

