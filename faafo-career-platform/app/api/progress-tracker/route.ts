import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || session?.user?.id;

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 401 }
      );
    }

    // Verify user access
    if (session?.user?.id !== userId) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized access' },
        { status: 403 }
      );
    }

    // Get user's learning progress
    const userProgress = await prisma.userLearningProgress.findMany({
      where: { userId },
      include: {
        resource: {
          select: {
            id: true,
            title: true,
            category: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    // Get user's ratings
    const userRatings = await prisma.resourceRating.findMany({
      where: { userId },
      include: {
        resource: {
          select: {
            id: true,
            title: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Calculate statistics
    const totalResources = userProgress.length;
    const completedResources = userProgress.filter(p => p.status === 'COMPLETED').length;
    const inProgressResources = userProgress.filter(p => p.status === 'IN_PROGRESS').length;
    const bookmarkedResources = userProgress.filter(p => p.status === 'BOOKMARKED').length;

    // Calculate average rating
    const averageRating = userRatings.length > 0 
      ? userRatings.reduce((sum, rating) => sum + rating.rating, 0) / userRatings.length
      : 0;

    // Calculate streak (simplified - consecutive days with activity)
    const streakDays = calculateStreakDays(userProgress);

    // Weekly goal and progress (simplified)
    const weeklyGoal = 3; // Default weekly goal
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    
    const weeklyProgress = userProgress.filter(p => 
      p.updatedAt >= oneWeekAgo && p.status === 'COMPLETED'
    ).length;

    // Recent activity
    const recentActivity = userProgress.slice(0, 10).map(progress => ({
      id: progress.id,
      resourceTitle: progress.resource.title,
      status: progress.status,
      date: progress.updatedAt.toISOString(),
      rating: progress.rating,
    }));

    // Generate achievements
    const achievements = generateAchievements(completedResources, userRatings.length, streakDays);

    const progressData = {
      totalResources,
      completedResources,
      inProgressResources,
      bookmarkedResources,
      averageRating: Math.round(averageRating * 10) / 10,
      totalRatings: userRatings.length,
      streakDays,
      weeklyGoal,
      weeklyProgress,
      recentActivity,
      achievements,
    };

    return NextResponse.json({
      success: true,
      data: progressData,
    });

  } catch (error) {
    console.error('Error fetching progress data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch progress data' },
      { status: 500 }
    );
  }
}

function calculateStreakDays(userProgress: { updatedAt: Date; status: string }[]): number {
  if (userProgress.length === 0) return 0;

  // Sort by date
  const sortedProgress = userProgress
    .filter(p => p.status === 'COMPLETED')
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

  if (sortedProgress.length === 0) return 0;

  let streak = 1;
  let currentDate = new Date(sortedProgress[0].updatedAt);
  currentDate.setHours(0, 0, 0, 0);

  for (let i = 1; i < sortedProgress.length; i++) {
    const progressDate = new Date(sortedProgress[i].updatedAt);
    progressDate.setHours(0, 0, 0, 0);
    
    const dayDifference = Math.floor((currentDate.getTime() - progressDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (dayDifference === 1) {
      streak++;
      currentDate = progressDate;
    } else if (dayDifference > 1) {
      break;
    }
  }

  return streak;
}

function generateAchievements(completedResources: number, totalRatings: number, streakDays: number) {
  const achievements: Array<{
    id: string;
    title: string;
    description: string;
    icon: string;
    unlockedAt: string;
  }> = [];

  // First completion
  if (completedResources >= 1) {
    achievements.push({
      id: 'first-completion',
      title: 'First Steps',
      description: 'Completed your first learning resource',
      icon: '🎯',
      unlockedAt: new Date().toISOString(),
    });
  }

  // Multiple completions
  if (completedResources >= 5) {
    achievements.push({
      id: 'five-completions',
      title: 'Learning Momentum',
      description: 'Completed 5 learning resources',
      icon: '🚀',
      unlockedAt: new Date().toISOString(),
    });
  }

  if (completedResources >= 10) {
    achievements.push({
      id: 'ten-completions',
      title: 'Dedicated Learner',
      description: 'Completed 10 learning resources',
      icon: '📚',
      unlockedAt: new Date().toISOString(),
    });
  }

  // Rating achievements
  if (totalRatings >= 3) {
    achievements.push({
      id: 'helpful-reviewer',
      title: 'Helpful Reviewer',
      description: 'Provided 3 resource ratings',
      icon: '⭐',
      unlockedAt: new Date().toISOString(),
    });
  }

  // Streak achievements
  if (streakDays >= 3) {
    achievements.push({
      id: 'three-day-streak',
      title: 'Consistent Learner',
      description: '3-day learning streak',
      icon: '🔥',
      unlockedAt: new Date().toISOString(),
    });
  }

  if (streakDays >= 7) {
    achievements.push({
      id: 'week-streak',
      title: 'Weekly Warrior',
      description: '7-day learning streak',
      icon: '💪',
      unlockedAt: new Date().toISOString(),
    });
  }

  return achievements;
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { weeklyGoal } = body;

    if (!weeklyGoal || weeklyGoal < 1 || weeklyGoal > 50) {
      return NextResponse.json(
        { success: false, error: 'Weekly goal must be between 1 and 50' },
        { status: 400 }
      );
    }

    // For now, we'll store this in user profile or a separate settings table
    // This is a simplified implementation
    
    return NextResponse.json({
      success: true,
      message: 'Weekly goal updated successfully',
      data: { weeklyGoal },
    });

  } catch (error) {
    console.error('Error updating weekly goal:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update weekly goal' },
      { status: 500 }
    );
  }
}
