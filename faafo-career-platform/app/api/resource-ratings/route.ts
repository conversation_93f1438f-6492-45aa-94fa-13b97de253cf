import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const resourceId = searchParams.get('resourceId');
    const userId = searchParams.get('userId');

    if (resourceId) {
      // Get ratings for specific resource
      const ratings = await prisma.resourceRating.findMany({
        where: {
          resourceId: resourceId
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Calculate average rating
      const averageRating = ratings.length > 0 
        ? ratings.reduce((sum, rating) => sum + rating.rating, 0) / ratings.length
        : 0;

      return NextResponse.json({
        success: true,
        data: {
          ratings,
          averageRating: Math.round(averageRating * 10) / 10,
          totalRatings: ratings.length
        }
      });
    } else if (userId) {
      // Get all ratings by specific user
      const session = await getServerSession(authOptions);
      
      if (!session || !session.user?.id || session.user.id !== userId) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const userRatings = await prisma.resourceRating.findMany({
        where: {
          userId: userId
        },
        include: {
          resource: {
            select: {
              id: true,
              title: true,
              url: true,
              category: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return NextResponse.json({
        success: true,
        data: userRatings
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Resource ID or User ID is required' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Error fetching resource ratings:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch resource ratings' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { resourceId, rating, review, isHelpful } = body;

    if (!resourceId || !rating) {
      return NextResponse.json(
        { success: false, error: 'Resource ID and rating are required' },
        { status: 400 }
      );
    }

    // Validate rating
    if (rating < 1 || rating > 5) {
      return NextResponse.json(
        { success: false, error: 'Rating must be between 1 and 5' },
        { status: 400 }
      );
    }

    // Check if resource exists
    const resource = await prisma.learningResource.findUnique({
      where: { id: resourceId }
    });

    if (!resource) {
      return NextResponse.json(
        { success: false, error: 'Resource not found' },
        { status: 404 }
      );
    }

    const resourceRating = await prisma.resourceRating.upsert({
      where: {
        userId_resourceId: {
          userId: session.user.id,
          resourceId: resourceId
        }
      },
      update: {
        rating: rating,
        review: review || undefined,
        isHelpful: isHelpful,
        updatedAt: new Date()
      },
      create: {
        userId: session.user.id,
        resourceId: resourceId,
        rating: rating,
        review: review || undefined,
        isHelpful: isHelpful
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        resource: {
          select: {
            id: true,
            title: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: resourceRating
    });

  } catch (error) {
    console.error('Error creating/updating resource rating:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create/update resource rating' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const resourceId = searchParams.get('resourceId');

    if (!resourceId) {
      return NextResponse.json(
        { success: false, error: 'Resource ID is required' },
        { status: 400 }
      );
    }

    await prisma.resourceRating.delete({
      where: {
        userId_resourceId: {
          userId: session.user.id,
          resourceId: resourceId
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Rating deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting resource rating:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete resource rating' },
      { status: 500 }
    );
  }
}
