import { NextRequest, NextResponse } from 'next/server';
import { swaggerSpec } from '@/lib/swagger/openapi';

// GET - Return OpenAPI specification
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const format = searchParams.get('format') || 'json';

  try {
    if (format === 'yaml') {
      // Convert to YAML if requested
      const yaml = require('js-yaml');
      const yamlSpec = yaml.dump(swaggerSpec);
      
      return new NextResponse(yamlSpec, {
        headers: {
          'Content-Type': 'application/x-yaml',
          'Content-Disposition': 'attachment; filename="faafo-api-spec.yaml"'
        }
      });
    }

    // Return JSON by default
    return NextResponse.json(swaggerSpec, {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      }
    });

  } catch (error) {
    console.error('Error generating API documentation:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate API documentation',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
