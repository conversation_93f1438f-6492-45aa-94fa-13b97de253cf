import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const { token, email } = await request.json();

    if (!token || !email) {
      return NextResponse.json({ error: 'Token and email are required.' }, { status: 400 });
    }

    // Find the verification token
    const verificationToken = await prisma.verificationToken.findUnique({
      where: {
        token: token,
      },
    });

    if (!verificationToken) {
      return NextResponse.json({ error: 'Invalid verification token.' }, { status: 400 });
    }

    // Check if token has expired
    if (verificationToken.expires < new Date()) {
      // Clean up expired token
      await prisma.verificationToken.delete({
        where: { token: token },
      });
      return NextResponse.json({ error: 'Verification token has expired.' }, { status: 400 });
    }

    // Check if the email matches
    if (verificationToken.identifier !== email) {
      return NextResponse.json({ error: 'Invalid verification token.' }, { status: 400 });
    }

    // Find the user
    const user = await prisma.user.findUnique({
      where: { email: email },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found.' }, { status: 404 });
    }

    // Check if user is already verified
    if (user.emailVerified) {
      // Clean up the token
      await prisma.verificationToken.delete({
        where: { token: token },
      });
      return NextResponse.json({ message: 'Email is already verified.' }, { status: 200 });
    }

    // Update user as verified and clean up token
    await prisma.$transaction([
      prisma.user.update({
        where: { id: user.id },
        data: {
          emailVerified: new Date(),
        },
      }),
      prisma.verificationToken.delete({
        where: { token: token },
      }),
    ]);

    return NextResponse.json({ message: 'Email verified successfully.' }, { status: 200 });

  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.json({ error: 'An error occurred while verifying your email.' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    const email = searchParams.get('email');

    if (!token || !email) {
      return NextResponse.json({ error: 'Token and email are required.' }, { status: 400 });
    }

    // Find the verification token
    const verificationToken = await prisma.verificationToken.findUnique({
      where: {
        token: token,
      },
    });

    if (!verificationToken) {
      return NextResponse.json({ error: 'Invalid verification token.' }, { status: 400 });
    }

    // Check if token has expired
    if (verificationToken.expires < new Date()) {
      return NextResponse.json({ error: 'Verification token has expired.' }, { status: 400 });
    }

    // Check if the email matches
    if (verificationToken.identifier !== email) {
      return NextResponse.json({ error: 'Invalid verification token.' }, { status: 400 });
    }

    // Find the user
    const user = await prisma.user.findUnique({
      where: { email: email },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found.' }, { status: 404 });
    }

    return NextResponse.json({ 
      valid: true, 
      email: email,
      alreadyVerified: !!user.emailVerified 
    }, { status: 200 });

  } catch (error) {
    console.error('Email verification check error:', error);
    return NextResponse.json({ error: 'An error occurred while checking verification token.' }, { status: 500 });
  }
}
