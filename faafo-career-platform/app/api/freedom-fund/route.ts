import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { Prisma } from '@prisma/client';

interface FreedomFundSaveRequest {
  monthlyExpenses: number;
  coverageMonths: number;
  currentSavingsAmount?: number;
  monthlyContribution?: number;
  adjustForInflation?: boolean;
}

interface FreedomFundUpdateRequest {
  monthlyExpenses?: number;
  coverageMonths?: number;
  currentSavingsAmount?: number;
  monthlyContribution?: number;
  adjustForInflation?: boolean;
}

// POST handler to create or update FreedomFund data
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json() as FreedomFundSaveRequest;
    const { monthlyExpenses, coverageMonths, currentSavingsAmount, monthlyContribution, adjustForInflation } = body;

    // Enhanced validation
    if (typeof monthlyExpenses !== 'number' || monthlyExpenses <= 0) {
      return NextResponse.json({ error: 'Monthly expenses must be a positive number.' }, { status: 400 });
    }

    if (typeof coverageMonths !== 'number' || ![3, 6, 9, 12].includes(coverageMonths)) {
      return NextResponse.json({ error: 'Coverage months must be 3, 6, 9, or 12.' }, { status: 400 });
    }

    if (currentSavingsAmount !== undefined && (typeof currentSavingsAmount !== 'number' || currentSavingsAmount < 0)) {
      return NextResponse.json({ error: 'Current savings amount must be a non-negative number.' }, { status: 400 });
    }

    if (monthlyContribution !== undefined && (typeof monthlyContribution !== 'number' || monthlyContribution < 0)) {
      return NextResponse.json({ error: 'Monthly contribution must be a non-negative number.' }, { status: 400 });
    }

    // Calculate target savings with optional inflation adjustment
    const baseTarget = monthlyExpenses * coverageMonths;
    const inflationRate = 0.03; // 3% annual inflation
    const targetSavings = adjustForInflation ? baseTarget * (1 + inflationRate) : baseTarget;

    const freedomFundEntry = await prisma.freedomFund.upsert({
      where: { userId: session.user.id },
      update: {
        monthlyExpenses,
        coverageMonths,
        targetSavings,
        currentSavingsAmount: currentSavingsAmount === undefined ? null : currentSavingsAmount,
      },
      create: {
        userId: session.user.id,
        monthlyExpenses,
        coverageMonths,
        targetSavings,
        currentSavingsAmount: currentSavingsAmount === undefined ? null : currentSavingsAmount,
      },
    });

    return NextResponse.json(freedomFundEntry, { status: 200 });

  } catch (error) {
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid JSON format in request body.' }, { status: 400 });
    }
    console.error('Freedom Fund POST error:', error);

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      // Handle specific Prisma errors
      if (error.code === 'P2002') {
        return NextResponse.json({ error: 'A Freedom Fund entry already exists for this user.' }, { status: 409 });
      }
      return NextResponse.json({ error: 'Database error processing your request.' }, { status: 500 });
    }

    return NextResponse.json({ error: 'An unexpected error occurred while saving your Freedom Fund data.' }, { status: 500 });
  }
}

// GET handler to retrieve FreedomFund data
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const freedomFundEntry = await prisma.freedomFund.findUnique({
      where: { userId: session.user.id },
    });

    if (!freedomFundEntry) {
      return NextResponse.json({ error: 'No Freedom Fund data found for this user.' }, { status: 404 });
    }

    return NextResponse.json(freedomFundEntry, { status: 200 });

  } catch (error) {
    console.error('Freedom Fund GET error:', error);

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      return NextResponse.json({ error: 'Database error retrieving your Freedom Fund data.' }, { status: 500 });
    }

    return NextResponse.json({ error: 'An unexpected error occurred while fetching your Freedom Fund data.' }, { status: 500 });
  }
}

// PUT handler to update specific fields of FreedomFund data
export async function PUT(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json() as FreedomFundUpdateRequest;
    const { monthlyExpenses, coverageMonths, currentSavingsAmount, monthlyContribution, adjustForInflation } = body;

    // Check if the Freedom Fund entry exists
    const existingEntry = await prisma.freedomFund.findUnique({
      where: { userId: session.user.id },
    });

    if (!existingEntry) {
      return NextResponse.json({ error: 'No Freedom Fund data found for this user. Please create one first.' }, { status: 404 });
    }

    // Validate provided fields
    if (monthlyExpenses !== undefined && (typeof monthlyExpenses !== 'number' || monthlyExpenses <= 0)) {
      return NextResponse.json({ error: 'Monthly expenses must be a positive number.' }, { status: 400 });
    }

    if (coverageMonths !== undefined && (typeof coverageMonths !== 'number' || ![3, 6, 9, 12].includes(coverageMonths))) {
      return NextResponse.json({ error: 'Coverage months must be 3, 6, 9, or 12.' }, { status: 400 });
    }

    if (currentSavingsAmount !== undefined && (typeof currentSavingsAmount !== 'number' || currentSavingsAmount < 0)) {
      return NextResponse.json({ error: 'Current savings amount must be a non-negative number.' }, { status: 400 });
    }

    if (monthlyContribution !== undefined && (typeof monthlyContribution !== 'number' || monthlyContribution < 0)) {
      return NextResponse.json({ error: 'Monthly contribution must be a non-negative number.' }, { status: 400 });
    }

    // Prepare update data
    const updateData: any = {};

    if (monthlyExpenses !== undefined) updateData.monthlyExpenses = monthlyExpenses;
    if (coverageMonths !== undefined) updateData.coverageMonths = coverageMonths;
    if (currentSavingsAmount !== undefined) updateData.currentSavingsAmount = currentSavingsAmount;

    // Recalculate target savings if monthly expenses or coverage months changed
    if (monthlyExpenses !== undefined || coverageMonths !== undefined) {
      const finalMonthlyExpenses = monthlyExpenses ?? existingEntry.monthlyExpenses;
      const finalCoverageMonths = coverageMonths ?? existingEntry.coverageMonths;
      const baseTarget = finalMonthlyExpenses * finalCoverageMonths;
      const inflationRate = 0.03;
      updateData.targetSavings = adjustForInflation ? baseTarget * (1 + inflationRate) : baseTarget;
    }

    const updatedEntry = await prisma.freedomFund.update({
      where: { userId: session.user.id },
      data: updateData,
    });

    return NextResponse.json(updatedEntry, { status: 200 });

  } catch (error) {
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid JSON format in request body.' }, { status: 400 });
    }
    console.error('Freedom Fund PUT error:', error);

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      return NextResponse.json({ error: 'Database error updating your Freedom Fund data.' }, { status: 500 });
    }

    return NextResponse.json({ error: 'An unexpected error occurred while updating your Freedom Fund data.' }, { status: 500 });
  }
}

// DELETE handler to remove FreedomFund data
export async function DELETE(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const existingEntry = await prisma.freedomFund.findUnique({
      where: { userId: session.user.id },
    });

    if (!existingEntry) {
      return NextResponse.json({ error: 'No Freedom Fund data found for this user.' }, { status: 404 });
    }

    await prisma.freedomFund.delete({
      where: { userId: session.user.id },
    });

    return NextResponse.json({ message: 'Freedom Fund data deleted successfully.' }, { status: 200 });

  } catch (error) {
    console.error('Freedom Fund DELETE error:', error);

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      return NextResponse.json({ error: 'Database error deleting your Freedom Fund data.' }, { status: 500 });
    }

    return NextResponse.json({ error: 'An unexpected error occurred while deleting your Freedom Fund data.' }, { status: 500 });
  }
}