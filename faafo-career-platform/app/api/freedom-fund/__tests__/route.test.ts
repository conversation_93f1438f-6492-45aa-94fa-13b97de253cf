import { NextRequest } from 'next/server';
import { GET, POST, PUT, DELETE } from '../route';
import { getServerSession } from 'next-auth/next';
import prisma from '@/lib/prisma';

// Mock dependencies
jest.mock('next-auth/next');
jest.mock('@/lib/prisma', () => ({
  freedomFund: {
    findUnique: jest.fn(),
    upsert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;
const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('/api/freedom-fund', () => {
  const mockSession = {
    user: {
      id: 'test-user-id',
      email: '<EMAIL>',
      name: 'Test User',
    },
  };

  const mockFreedomFundData = {
    id: 'test-freedom-fund-id',
    userId: 'test-user-id',
    monthlyExpenses: 3000,
    coverageMonths: 6,
    targetSavings: 18000,
    currentSavingsAmount: 5000,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/freedom-fund', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 404 when no freedom fund data exists', async () => {
      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.freedomFund.findUnique.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('No Freedom Fund data found for this user.');
    });

    it('should return freedom fund data when it exists', async () => {
      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.freedomFund.findUnique.mockResolvedValue(mockFreedomFundData);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockFreedomFundData);
    });
  });

  describe('POST /api/freedom-fund', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund', {
        method: 'POST',
        body: JSON.stringify({
          monthlyExpenses: 3000,
          coverageMonths: 6,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 400 for invalid monthly expenses', async () => {
      mockGetServerSession.mockResolvedValue(mockSession);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund', {
        method: 'POST',
        body: JSON.stringify({
          monthlyExpenses: -100,
          coverageMonths: 6,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Monthly expenses must be a positive number.');
    });

    it('should return 400 for invalid coverage months', async () => {
      mockGetServerSession.mockResolvedValue(mockSession);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund', {
        method: 'POST',
        body: JSON.stringify({
          monthlyExpenses: 3000,
          coverageMonths: 5, // Invalid - must be 3, 6, 9, or 12
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Coverage months must be 3, 6, 9, or 12.');
    });

    it('should create freedom fund data successfully', async () => {
      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.freedomFund.upsert.mockResolvedValue(mockFreedomFundData);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund', {
        method: 'POST',
        body: JSON.stringify({
          monthlyExpenses: 3000,
          coverageMonths: 6,
          currentSavingsAmount: 5000,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockFreedomFundData);
      expect(mockPrisma.freedomFund.upsert).toHaveBeenCalledWith({
        where: { userId: 'test-user-id' },
        update: {
          monthlyExpenses: 3000,
          coverageMonths: 6,
          targetSavings: 18000,
          currentSavingsAmount: 5000,
        },
        create: {
          userId: 'test-user-id',
          monthlyExpenses: 3000,
          coverageMonths: 6,
          targetSavings: 18000,
          currentSavingsAmount: 5000,
        },
      });
    });

    it('should calculate target savings with inflation adjustment', async () => {
      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.freedomFund.upsert.mockResolvedValue({
        ...mockFreedomFundData,
        targetSavings: 18540, // 18000 * 1.03
      });

      const request = new NextRequest('http://localhost:3000/api/freedom-fund', {
        method: 'POST',
        body: JSON.stringify({
          monthlyExpenses: 3000,
          coverageMonths: 6,
          adjustForInflation: true,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(mockPrisma.freedomFund.upsert).toHaveBeenCalledWith(
        expect.objectContaining({
          update: expect.objectContaining({
            targetSavings: 18540,
          }),
          create: expect.objectContaining({
            targetSavings: 18540,
          }),
        })
      );
    });
  });

  describe('PUT /api/freedom-fund', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund', {
        method: 'PUT',
        body: JSON.stringify({
          currentSavingsAmount: 6000,
        }),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 404 when no freedom fund data exists', async () => {
      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.freedomFund.findUnique.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund', {
        method: 'PUT',
        body: JSON.stringify({
          currentSavingsAmount: 6000,
        }),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('No Freedom Fund data found for this user. Please create one first.');
    });

    it('should update freedom fund data successfully', async () => {
      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.freedomFund.findUnique.mockResolvedValue(mockFreedomFundData);
      mockPrisma.freedomFund.update.mockResolvedValue({
        ...mockFreedomFundData,
        currentSavingsAmount: 6000,
      });

      const request = new NextRequest('http://localhost:3000/api/freedom-fund', {
        method: 'PUT',
        body: JSON.stringify({
          currentSavingsAmount: 6000,
        }),
      });

      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.currentSavingsAmount).toBe(6000);
      expect(mockPrisma.freedomFund.update).toHaveBeenCalledWith({
        where: { userId: 'test-user-id' },
        data: { currentSavingsAmount: 6000 },
      });
    });
  });

  describe('DELETE /api/freedom-fund', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund', {
        method: 'DELETE',
      });

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 404 when no freedom fund data exists', async () => {
      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.freedomFund.findUnique.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund', {
        method: 'DELETE',
      });

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('No Freedom Fund data found for this user.');
    });

    it('should delete freedom fund data successfully', async () => {
      mockGetServerSession.mockResolvedValue(mockSession);
      mockPrisma.freedomFund.findUnique.mockResolvedValue(mockFreedomFundData);
      mockPrisma.freedomFund.delete.mockResolvedValue(mockFreedomFundData);

      const request = new NextRequest('http://localhost:3000/api/freedom-fund', {
        method: 'DELETE',
      });

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Freedom Fund data deleted successfully.');
      expect(mockPrisma.freedomFund.delete).toHaveBeenCalledWith({
        where: { userId: 'test-user-id' },
      });
    });
  });
});
