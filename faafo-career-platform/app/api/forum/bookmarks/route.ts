import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/forum/bookmarks - Get user's bookmarked posts
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Get user's bookmarked posts
    const bookmarks = await prisma.forumBookmark.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        post: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            category: true,
            _count: {
              select: {
                replies: true,
                reactions: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await prisma.forumBookmark.count({
      where: {
        userId: session.user.id,
      },
    });

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      bookmarks: bookmarks.map(bookmark => ({
        id: bookmark.id,
        createdAt: bookmark.createdAt,
        post: bookmark.post,
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });

  } catch (error) {
    console.error('Error fetching bookmarks:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/forum/bookmarks - Add bookmark
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { postId } = body;

    if (!postId) {
      return NextResponse.json(
        { error: 'Post ID is required' },
        { status: 400 }
      );
    }

    // Check if post exists
    const post = await prisma.forumPost.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return NextResponse.json(
        { error: 'Post not found' },
        { status: 404 }
      );
    }

    // Check if bookmark already exists
    const existingBookmark = await prisma.forumBookmark.findUnique({
      where: {
        userId_postId: {
          userId: session.user.id,
          postId,
        },
      },
    });

    if (existingBookmark) {
      return NextResponse.json(
        { error: 'Post already bookmarked' },
        { status: 409 }
      );
    }

    // Create bookmark
    const bookmark = await prisma.forumBookmark.create({
      data: {
        userId: session.user.id,
        postId,
      },
      include: {
        post: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Post bookmarked successfully',
      bookmark,
    });

  } catch (error) {
    console.error('Error creating bookmark:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/forum/bookmarks - Remove bookmark
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { postId, bookmarkId } = body;

    if (!postId && !bookmarkId) {
      return NextResponse.json(
        { error: 'Post ID or Bookmark ID is required' },
        { status: 400 }
      );
    }

    let bookmark;

    if (bookmarkId) {
      // Delete by bookmark ID
      bookmark = await prisma.forumBookmark.findFirst({
        where: {
          id: bookmarkId,
          userId: session.user.id,
        },
      });
    } else {
      // Delete by post ID
      bookmark = await prisma.forumBookmark.findFirst({
        where: {
          postId,
          userId: session.user.id,
        },
      });
    }

    if (!bookmark) {
      return NextResponse.json(
        { error: 'Bookmark not found' },
        { status: 404 }
      );
    }

    await prisma.forumBookmark.delete({
      where: { id: bookmark.id },
    });

    return NextResponse.json({
      message: 'Bookmark removed successfully',
    });

  } catch (error) {
    console.error('Error removing bookmark:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
