'use client';

import { useEffect, useRef } from 'react';
import SwaggerUI from 'swagger-ui-react';
import 'swagger-ui-react/swagger-ui.css';

export default function ApiDocsPage() {
  const swaggerUIRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Custom CSS for better integration with the app theme
    const style = document.createElement('style');
    style.textContent = `
      .swagger-ui {
        font-family: inherit;
      }
      .swagger-ui .topbar {
        display: none;
      }
      .swagger-ui .info {
        margin: 20px 0;
      }
      .swagger-ui .scheme-container {
        background: transparent;
        box-shadow: none;
        padding: 0;
      }
      .swagger-ui .info .title {
        color: #1f2937;
      }
      .swagger-ui .info .description {
        color: #4b5563;
      }
      .swagger-ui .opblock.opblock-get {
        border-color: #10b981;
        background: rgba(16, 185, 129, 0.1);
      }
      .swagger-ui .opblock.opblock-post {
        border-color: #3b82f6;
        background: rgba(59, 130, 246, 0.1);
      }
      .swagger-ui .opblock.opblock-put {
        border-color: #f59e0b;
        background: rgba(245, 158, 11, 0.1);
      }
      .swagger-ui .opblock.opblock-delete {
        border-color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
      }
      .swagger-ui .btn.authorize {
        background-color: #3b82f6;
        border-color: #3b82f6;
      }
      .swagger-ui .btn.authorize:hover {
        background-color: #2563eb;
        border-color: #2563eb;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const requestInterceptor = (request: any) => {
    // Add authentication headers if available
    const sessionToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('next-auth.session-token='))
      ?.split('=')[1];

    if (sessionToken) {
      request.headers['Cookie'] = `next-auth.session-token=${sessionToken}`;
    }

    return request;
  };

  const responseInterceptor = (response: any) => {
    // Handle response errors gracefully
    if (response.status >= 400) {
      console.warn('API request failed:', response.status, response.statusText);
    }
    return response;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                FAAFO Career Platform API Documentation
              </h1>
              <p className="mt-2 text-gray-600">
                Interactive API documentation for developers
              </p>
            </div>
            <div className="flex space-x-4">
              <a
                href="/api/docs"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                target="_blank"
                rel="noopener noreferrer"
              >
                Download JSON
              </a>
              <a
                href="/api/docs?format=yaml"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                target="_blank"
                rel="noopener noreferrer"
              >
                Download YAML
              </a>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Getting Started
              </h2>
              <div className="prose prose-sm text-gray-600">
                <p>
                  This API documentation provides comprehensive information about all available endpoints
                  in the FAAFO Career Platform. The API uses session-based authentication and follows
                  RESTful conventions.
                </p>
                <div className="mt-4 p-4 bg-blue-50 rounded-md">
                  <h3 className="text-sm font-medium text-blue-900 mb-2">Authentication</h3>
                  <p className="text-sm text-blue-800">
                    Most endpoints require authentication. Make sure you're logged in to test the API endpoints.
                    The interactive documentation below will automatically include your session cookies.
                  </p>
                </div>
                <div className="mt-4 p-4 bg-yellow-50 rounded-md">
                  <h3 className="text-sm font-medium text-yellow-900 mb-2">Rate Limiting</h3>
                  <p className="text-sm text-yellow-800">
                    All endpoints are rate-limited to ensure fair usage. Limits vary by endpoint type
                    and are documented in each endpoint's description.
                  </p>
                </div>
              </div>
            </div>

            <div ref={swaggerUIRef}>
              <SwaggerUI
                url="/api/docs"
                requestInterceptor={requestInterceptor}
                responseInterceptor={responseInterceptor}
                docExpansion="list"
                defaultModelsExpandDepth={1}
                defaultModelExpandDepth={1}
                displayOperationId={false}
                displayRequestDuration={true}
                filter={true}
                showExtensions={true}
                showCommonExtensions={true}
                tryItOutEnabled={true}
                supportedSubmitMethods={['get', 'post', 'put', 'delete', 'patch']}

                plugins={[]}
                layout="BaseLayout"
              />
            </div>
          </div>
        </div>

        <div className="mt-8 bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            API Usage Examples
          </h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                JavaScript/TypeScript
              </h3>
              <pre className="bg-gray-100 rounded-md p-4 text-sm overflow-x-auto">
                <code>{`// Fetch learning resources
const response = await fetch('/api/learning-resources', {
  method: 'GET',
  credentials: 'include', // Include session cookies
  headers: {
    'Content-Type': 'application/json',
  },
});

const data = await response.json();
if (data.success) {
  console.log('Learning resources:', data.data);
}`}</code>
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Python
              </h3>
              <pre className="bg-gray-100 rounded-md p-4 text-sm overflow-x-auto">
                <code>{`import requests

# Get learning resources
response = requests.get(
    'https://your-domain.com/api/learning-resources',
    headers={'Content-Type': 'application/json'},
    cookies={'next-auth.session-token': 'your-session-token'}
)

if response.status_code == 200:
    data = response.json()
    if data['success']:
        print('Learning resources:', data['data'])`}</code>
              </pre>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                cURL
              </h3>
              <pre className="bg-gray-100 rounded-md p-4 text-sm overflow-x-auto">
                <code>{`# Get learning resources
curl -X GET "https://your-domain.com/api/learning-resources" \\
  -H "Content-Type: application/json" \\
  -H "Cookie: next-auth.session-token=your-session-token"

# Create a new learning resource (admin only)
curl -X POST "https://your-domain.com/api/learning-resources" \\
  -H "Content-Type: application/json" \\
  -H "Cookie: next-auth.session-token=your-session-token" \\
  -d '{
    "title": "Introduction to React",
    "description": "Learn the basics of React",
    "type": "COURSE",
    "url": "https://example.com/react-course",
    "category": "WEB_DEVELOPMENT",
    "skillLevel": "BEGINNER"
  }'`}</code>
              </pre>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Error Handling
          </h2>
          
          <div className="prose prose-sm text-gray-600">
            <p>
              All API responses follow a consistent format. Successful responses include a 
              <code className="bg-gray-100 px-1 rounded">success: true</code> field, while 
              errors include <code className="bg-gray-100 px-1 rounded">success: false</code> 
              and an <code className="bg-gray-100 px-1 rounded">error</code> message.
            </p>
            
            <div className="mt-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">Common HTTP Status Codes</h3>
              <ul className="text-sm space-y-1">
                <li><strong>200 OK</strong> - Request successful</li>
                <li><strong>201 Created</strong> - Resource created successfully</li>
                <li><strong>400 Bad Request</strong> - Invalid request data</li>
                <li><strong>401 Unauthorized</strong> - Authentication required</li>
                <li><strong>403 Forbidden</strong> - Insufficient permissions</li>
                <li><strong>404 Not Found</strong> - Resource not found</li>
                <li><strong>429 Too Many Requests</strong> - Rate limit exceeded</li>
                <li><strong>500 Internal Server Error</strong> - Server error</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
