"use client";

import React from "react";
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { Send, GraduationCap, DollarSign, Lightbulb, MessageSquare, Briefcase, BookOpen } from "lucide-react";
import { Button } from "@/components/ui/button";


interface HeroSectionProps {
  title: string;
  subtitle: string;
  primaryAction: {
    text: string;
    href: string;
    icon?: React.ReactNode;
  };
  secondaryAction: {
    text: string;
    href: string;
  };
}

function HeroSection({
  title,
  subtitle,
  primaryAction,
  secondaryAction,
}: HeroSectionProps) {
  return (
    <section className="relative min-h-[calc(100vh-80px)] flex flex-col items-center justify-center bg-background text-foreground pt-20 pb-32 px-4 overflow-hidden text-center">
      <div className="relative z-10 flex flex-col items-center gap-8">
        <h1 className="text-5xl sm:text-6xl md:text-7xl font-bold leading-tight">
          {title}
        </h1>
        <p className="text-lg sm:text-xl md:text-2xl max-w-2xl text-foreground">
          {subtitle}
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4 mt-6">
          <Button size="lg" asChild className="bg-primary hover:bg-primary/90 text-primary-foreground text-lg px-8 py-3">
            <Link href={primaryAction.href} className="flex items-center gap-2">
              {primaryAction.icon}
              {primaryAction.text}
            </Link>
          </Button>
          <Button size="lg" asChild className="bg-secondary hover:bg-secondary/80 text-secondary-foreground text-lg px-8 py-3">
            <Link href={secondaryAction.href}>
              {secondaryAction.text}
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg text-center flex flex-col items-center border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
      <div className="text-gray-600 dark:text-gray-400 mb-4">{icon}</div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-gray-600 dark:text-gray-400 text-center">{description}</p>
    </div>
  );
}

export default function Home() {
  const { status } = useSession();

  return (
    <div className="min-h-screen bg-background">
      <HeroSection
        title="Find Your Path. Build a Fulfilling Career."
        subtitle="Join 10,000+ users taking control of their careers."
        primaryAction={{
          text: status === 'authenticated' ? "Go to Dashboard" : "Get Started",
          href: status === 'authenticated' ? "/dashboard" : "/signup",
          icon: <Send className="h-5 w-5 transform -rotate-45" />,
        }}
        secondaryAction={{
          text: "Explore Career Paths",
          href: "/career-paths",
        }}
      />

      {/* Features Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900 text-foreground px-4">
        <div className="container mx-auto text-center max-w-4xl">
          <h2 className="text-4xl font-bold mb-12">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Link href="/assessment">
              <FeatureCard
                icon={<GraduationCap className="h-12 w-12" />}
                title="Self-Assessment"
                description="Take our comprehensive assessment to discover career paths that match your goals and situation."
              />
            </Link>
            <Link href="/career-paths">
              <FeatureCard
                icon={<Briefcase className="h-12 w-12" />}
                title="Career Path Exploration"
                description="Explore detailed career paths with pros, cons, and actionable step-by-step guides."
              />
            </Link>
            <Link href="/freedom-fund">
              <FeatureCard
                icon={<DollarSign className="h-12 w-12" />}
                title="Freedom Fund Calculator"
                description="Calculate your financial runway and track your progress towards career transition security."
              />
            </Link>
            <Link href="/resources">
              <FeatureCard
                icon={<BookOpen className="h-12 w-12" />}
                title="Mindset Resources"
                description="Access curated resources to overcome fears, build confidence, and maintain motivation."
              />
            </Link>
            <Link href="/forum">
              <FeatureCard
                icon={<MessageSquare className="h-12 w-12" />}
                title="Community Support"
                description="Connect with others on similar journeys, share experiences, and get peer support."
              />
            </Link>
            <div className="md:col-span-2 lg:col-span-1">
              <FeatureCard
                icon={<Lightbulb className="h-12 w-12" />}
                title="AI-Powered Insights"
                description="Get personalized recommendations and insights powered by advanced AI technology."
              />
            </div>
          </div>
        </div>
      </section>

      {/* Authenticated User Section */}
      {status === 'authenticated' && (
        <section className="py-16 bg-gray-50 dark:bg-gray-900 text-foreground px-4">
          <div className="container mx-auto text-center max-w-4xl">
            <h2 className="text-3xl font-bold mb-6">Welcome back! Ready to continue your journey?</h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
              Pick up where you left off or explore new features to accelerate your career transition.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Button asChild size="lg" className="h-auto p-6 flex flex-col gap-2">
                <Link href="/dashboard">
                  <GraduationCap className="h-8 w-8" />
                  <span className="font-semibold">View Dashboard</span>
                  <span className="text-sm opacity-80">See your progress</span>
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="h-auto p-6 flex flex-col gap-2 border-2 border-gray-400 dark:border-gray-500 hover:border-gray-500 dark:hover:border-gray-400">
                <Link href="/assessment">
                  <Briefcase className="h-8 w-8" />
                  <span className="font-semibold">Take Assessment</span>
                  <span className="text-sm opacity-80">Get recommendations</span>
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="h-auto p-6 flex flex-col gap-2 border-2 border-gray-400 dark:border-gray-500 hover:border-gray-500 dark:hover:border-gray-400">
                <Link href="/forum">
                  <MessageSquare className="h-8 w-8" />
                  <span className="font-semibold">Join Discussion</span>
                  <span className="text-sm opacity-80">Connect with community</span>
                </Link>
              </Button>
            </div>
          </div>
        </section>
      )}
    </div>
  );
}
