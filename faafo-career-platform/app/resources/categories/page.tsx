'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowLeft, Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ResourceCategories from '@/components/resources/ResourceCategories';

export default function ResourceCategoriesPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <Button variant="ghost" asChild className="mb-4">
          <Link href="/resources" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Resources
          </Link>
        </Button>
        
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Browse by Category
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Explore learning resources organized by career field and skill domain. 
            Each category contains curated resources to help you build expertise in that area.
          </p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-8 flex flex-wrap gap-4 justify-center">
        <Button asChild variant="outline">
          <Link href="/resources" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Search All Resources
          </Link>
        </Button>
        
        <Button asChild variant="outline">
          <Link href="/resources?type=course" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            View Courses Only
          </Link>
        </Button>
        
        <Button asChild variant="outline">
          <Link href="/resources?cost=free" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Free Resources
          </Link>
        </Button>
      </div>

      {/* Categories Grid */}
      <div className="mb-12">
        <ResourceCategories />
      </div>

      {/* Additional Information */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-8">
        <h2 className="text-2xl font-semibold text-blue-900 dark:text-blue-100 mb-4">
          💡 How to Choose the Right Category
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-blue-800 dark:text-blue-200">
          <div>
            <h3 className="font-semibold mb-2">For Career Changers</h3>
            <ul className="space-y-1 text-sm">
              <li>• Start with <strong>beginner-level</strong> resources in your target field</li>
              <li>• Focus on <strong>free courses</strong> to explore before committing</li>
              <li>• Look for <strong>certification programs</strong> to validate your skills</li>
              <li>• Check the <strong>rating and reviews</strong> from other learners</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-2">For Skill Enhancement</h3>
            <ul className="space-y-1 text-sm">
              <li>• Choose <strong>intermediate or advanced</strong> resources</li>
              <li>• Look for <strong>specialized topics</strong> in your current field</li>
              <li>• Consider <strong>project-based learning</strong> for practical skills</li>
              <li>• Explore <strong>emerging technologies</strong> in your domain</li>
            </ul>
          </div>
        </div>

        <div className="mt-6 p-4 bg-white dark:bg-blue-900/40 rounded-lg">
          <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
            🎯 Recommended Learning Path
          </h3>
          <p className="text-sm text-blue-800 dark:text-blue-200">
            1. Take our <Link href="/assessment" className="underline font-medium">Career Assessment</Link> to get personalized recommendations
            2. Start with foundational resources in your chosen category
            3. Progress to intermediate and advanced materials
            4. Apply your learning through projects and real-world practice
            5. Join our <Link href="/forum" className="underline font-medium">Community Forum</Link> to connect with others in your field
          </p>
        </div>
      </div>

      {/* Popular Resources Teaser */}
      <div className="mt-12 text-center">
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Ready to Start Learning?
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Browse our complete collection of resources or get personalized recommendations
        </p>
        <div className="flex gap-4 justify-center">
          <Button asChild size="lg">
            <Link href="/resources">View All Resources</Link>
          </Button>
          <Button asChild variant="outline" size="lg">
            <Link href="/recommendations">Get Recommendations</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
