import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { ThemeProvider } from 'next-themes';
import SessionWrapper from '@/components/SessionWrapper';
import ErrorBoundary from '@/components/ErrorBoundary';
import CookieConsentBanner from '@/components/CookieConsentBanner';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'FAAFO Career Platform',
  description: 'Find And Apply For Opportunities - Your comprehensive career development platform',
  keywords: 'career, jobs, opportunities, professional development, skills assessment',
  authors: [{ name: 'FAAFO Team' }],
  openGraph: {
    title: 'FAAFO Career Platform',
    description: 'Find And Apply For Opportunities - Your comprehensive career development platform',
    type: 'website',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ErrorBoundary>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <SessionWrapper>
              {children}
              <CookieConsentBanner />
            </SessionWrapper>
          </ThemeProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
